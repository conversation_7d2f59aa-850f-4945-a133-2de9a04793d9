// API service for backend communication

export const API_BASE_URL = 'http://localhost:3000/api/v2';

export interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  phone: string;
  role: 'user' | 'admin';
  createdAt: string;
}

export interface LoginResponse {
  success: boolean;
  message: string;
  data?: {
    user: User;
    token: string;
  };
}

export interface RegisterResponse {
  success: boolean;
  message: string;
  data?: {
    user: User;
    token: string;
  };
}

export interface ApiError {
  success: false;
  message: string;
  errors?: any;
}

// Helper function to make API requests
const apiRequest = async (endpoint: string, options: RequestInit = {}): Promise<any> => {
  const url = `${API_BASE_URL}${endpoint}`;
  
  const defaultHeaders: Record<string, string> = {
    'Content-Type': 'application/json',
  };

  // Add auth token if available
  const token = localStorage.getItem('authToken');
  if (token) {
    defaultHeaders['Authorization'] = `Bearer ${token}`;
  }

  const config: RequestInit = {
    ...options,
    headers: {
      ...defaultHeaders,
      ...options.headers,
    },
  };

  try {
    const response = await fetch(url, config);
    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.message || 'API request failed');
    }

    return data;
  } catch (error) {
    console.error('API request error:', error);
    throw error;
  }
};

// Auth API functions
export const authAPI = {
  // Login user
  login: async (email: string, password: string): Promise<LoginResponse> => {
    const response = await apiRequest('/auth/login', {
      method: 'POST',
      body: JSON.stringify({ email, password }),
    });

    // Store token and user data if login successful
    if (response.success && response.data?.token) {
      localStorage.setItem('authToken', response.data.token);
      localStorage.setItem('currentUser', JSON.stringify(response.data.user));

      // Also store email and userId for easy access
      if (response.data.user?.email) {
        localStorage.setItem('userEmail', response.data.user.email);
      }
      if (response.data.user?._id || response.data.user?.id) {
        localStorage.setItem('userId', response.data.user._id || response.data.user.id);
      }
    }

    return response;
  },

  // Register user
  register: async (userData: {
    firstName: string;
    lastName: string;
    email: string;
    phone: string;
    password: string;
  }): Promise<RegisterResponse> => {
    const response = await apiRequest('/auth/register', {
      method: 'POST',
      body: JSON.stringify(userData),
    });

    // Store token if registration successful
    if (response.success && response.data?.token) {
      localStorage.setItem('authToken', response.data.token);
      localStorage.setItem('currentUser', JSON.stringify(response.data.user));
    }

    return response;
  },

  // Logout user
  logout: async (): Promise<void> => {
    try {
      await apiRequest('/auth/logout', {
        method: 'POST',
      });
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      // Always clear local storage
      localStorage.removeItem('authToken');
      localStorage.removeItem('currentUser');
    }
  },

  // Get current user info
  me: async (): Promise<{ success: boolean; data?: User }> => {
    return await apiRequest('/auth/me');
  },

  // Verify token
  verify: async (): Promise<{ success: boolean; data?: User }> => {
    return await apiRequest('/auth/verify');
  },

  // Check if email exists and get user role
  checkEmail: async (email: string): Promise<{ success: boolean; exists: boolean; user?: any }> => {
    return await apiRequest('/auth/check-email', {
      method: 'POST',
      body: JSON.stringify({ email }),
    });
  },

  // Login with email only (for regular users)
  loginEmailOnly: async (email: string): Promise<LoginResponse> => {
    const response = await apiRequest('/auth/login-email-only', {
      method: 'POST',
      body: JSON.stringify({ email }),
    });

    // Store token if login successful
    if (response.success && response.data?.token) {
      localStorage.setItem('authToken', response.data.token);
      localStorage.setItem('currentUser', JSON.stringify(response.data.user));
    }

    return response;
  },

  // Check if email exists and get user role
  checkEmail: async (email: string): Promise<{ success: boolean; exists: boolean; user?: any }> => {
    return await apiRequest('/auth/check-email', {
      method: 'POST',
      body: JSON.stringify({ email }),
    });
  },

  // Login with email only (for regular users)
  loginEmailOnly: async (email: string): Promise<LoginResponse> => {
    const response = await apiRequest('/auth/login-email-only', {
      method: 'POST',
      body: JSON.stringify({ email }),
    });

    // Store token if login successful
    if (response.success && response.data?.token) {
      localStorage.setItem('authToken', response.data.token);
      localStorage.setItem('currentUser', JSON.stringify(response.data.user));
    }

    return response;
  },
};

// Utility functions for local auth state
export const getCurrentUser = (): User | null => {
  const user = localStorage.getItem('currentUser');
  return user ? JSON.parse(user) : null;
};

export const isAuthenticated = (): boolean => {
  return !!localStorage.getItem('authToken');
};

export const isAdmin = (): boolean => {
  const user = getCurrentUser();
  return user?.role === 'admin';
};

export const clearAuthData = (): void => {
  localStorage.removeItem('authToken');
  localStorage.removeItem('currentUser');
  localStorage.removeItem('userEmail');
  localStorage.removeItem('userId');
};

// Helper function to save user data to localStorage
export const saveUserData = (user: any, token?: string): void => {
  if (token) {
    localStorage.setItem('authToken', token);
  }
  localStorage.setItem('currentUser', JSON.stringify(user));

  // Also store email and userId for easy access
  if (user?.email) {
    localStorage.setItem('userEmail', user.email);
  }
  if (user?.id) {
    localStorage.setItem('userId', user.id);
  }
};

// Loading states management
export const createLoadingState = () => {
  let isLoading = false;
  let error: string | null = null;

  return {
    setLoading: (loading: boolean) => { isLoading = loading; },
    setError: (err: string | null) => { error = err; },
    getLoading: () => isLoading,
    getError: () => error,
    clearError: () => { error = null; },
  };
};

// Error handling utility
export const handleApiError = (error: any): string => {
  if (error instanceof Error) {
    return error.message;
  }
  if (typeof error === 'string') {
    return error;
  }
  return 'An unexpected error occurred';
};

// Helper function to get stored user email and ID
export const getStoredUserData = (): { email?: string; userId?: string } => {
  return {
    email: localStorage.getItem('userEmail') || undefined,
    userId: localStorage.getItem('userId') || undefined
  };
};

// Dashboard API
export const dashboardAPI = {
  // Get user dashboard data using stored email/userId
  getUserDashboard: async (): Promise<any> => {
    const { email, userId } = getStoredUserData();

    if (!email && !userId) {
      throw new Error('No user data found in localStorage');
    }

    const params = new URLSearchParams();
    if (userId) {
      params.set('userId', userId);
    } else if (email) {
      params.set('email', email);
    }

    const response = await fetch(`${API_BASE_URL}/appointments/user?${params.toString()}`);

    if (!response.ok) {
      throw new Error('Failed to fetch dashboard data');
    }

    return await response.json();
  }
};
