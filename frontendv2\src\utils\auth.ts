
export interface User {
  id: string;
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  phone: string;
  role: 'user' | 'admin';
  createdAt: string;
  lastLogin?: string;
}

export interface Appointment {
  id: string;
  userId: string;
  serviceId: string;
  serviceName: string;
  servicePrice: number;
  addOns: Array<{
    id: string;
    name: string;
    price: number;
    duration: number;
  }>;
  date: string;
  time: string;
  status: 'pending' | 'confirmed' | 'completed' | 'cancelled';
  customerInfo: {
    firstName: string;
    lastName: string;
    phone: string;
    email: string;
  };
  totalPrice: number;
  depositPaid: boolean;
  depositAmount: number;
  balanceDue: number;
  createdAt: string;
  notes?: string;
}

// Initialize demo data
export const initializeDemoData = () => {
  // Demo users
  const demoUsers: User[] = [
    {
      id: 'user-1',
      email: '<EMAIL>',
      password: 'password123',
      firstName: 'Jane',
      lastName: 'Smith',
      phone: '+1234567890',
      role: 'user',
      createdAt: new Date().toISOString(),
      lastLogin: new Date().toISOString()
    },
    {
      id: 'admin-1',
      email: '<EMAIL>',
      password: 'admin123',
      firstName: 'Dammy',
      lastName: 'Admin',
      phone: '+1987654321',
      role: 'admin',
      createdAt: new Date().toISOString(),
      lastLogin: new Date().toISOString()
    }
  ];

  // Demo appointments
  const demoAppointments: Appointment[] = [
    {
      id: 'apt-1',
      userId: 'user-1',
      serviceId: '35420512',
      serviceName: '4-5 week Sisterlock Retightening with dammyspicybeauty',
      servicePrice: 165.00,
      addOns: [
        {
          id: 'apple-cider',
          name: 'Apple Cider Vinegar Detox',
          price: 55,
          duration: 30
        }
      ],
      date: '2025-08-19',
      time: '7:00 AM',
      status: 'confirmed',
      customerInfo: {
        firstName: 'Jane',
        lastName: 'Smith',
        phone: '+1234567890',
        email: '<EMAIL>'
      },
      totalPrice: 220.00,
      depositPaid: true,
      depositAmount: 30.00,
      balanceDue: 190.00,
      createdAt: new Date(Date.now() - 86400000).toISOString(), // 1 day ago
      notes: 'Regular client, prefers morning appointments'
    },
    {
      id: 'apt-2',
      userId: 'user-1',
      serviceId: '31196455',
      serviceName: 'Sisterlocks/Microlock Consultation',
      servicePrice: 60.00,
      addOns: [],
      date: '2025-09-15',
      time: '11:00 AM',
      status: 'pending',
      customerInfo: {
        firstName: 'Jane',
        lastName: 'Smith',
        phone: '+1234567890',
        email: '<EMAIL>'
      },
      totalPrice: 60.00,
      depositPaid: false,
      depositAmount: 30.00,
      balanceDue: 60.00,
      createdAt: new Date().toISOString(),
    }
  ];

  // Only initialize if data doesn't exist
  if (!localStorage.getItem('users')) {
    localStorage.setItem('users', JSON.stringify(demoUsers));
  }
  if (!localStorage.getItem('appointments')) {
    localStorage.setItem('appointments', JSON.stringify(demoAppointments));
  }
};

// User management functions
export const getUsers = (): User[] => {
  const users = localStorage.getItem('users');
  return users ? JSON.parse(users) : [];
};

export const saveUsers = (users: User[]) => {
  localStorage.setItem('users', JSON.stringify(users));
};

export const getUserByEmail = (email: string): User | null => {
  const users = getUsers();
  return users.find(user => user.email === email) || null;
};

export const createUser = (userData: Omit<User, 'id' | 'createdAt'>): User => {
  const users = getUsers();
  const newUser: User = {
    ...userData,
    id: `user-${Date.now()}`,
    createdAt: new Date().toISOString()
  };
  users.push(newUser);
  saveUsers(users);
  return newUser;
};

// Authentication functions
export const login = (email: string, password: string): User | null => {
  const user = getUserByEmail(email);
  if (user && user.password === password) {
    // Update last login
    const users = getUsers();
    const userIndex = users.findIndex(u => u.id === user.id);
    if (userIndex !== -1) {
      users[userIndex].lastLogin = new Date().toISOString();
      saveUsers(users);
    }
    
    // Store current user in localStorage
    localStorage.setItem('currentUser', JSON.stringify(user));
    return user;
  }
  return null;
};

export const logout = () => {
  localStorage.removeItem('currentUser');
};

export const getCurrentUser = (): User | null => {
  const user = localStorage.getItem('currentUser');
  return user ? JSON.parse(user) : null;
};

export const isAuthenticated = (): boolean => {
  return getCurrentUser() !== null;
};

export const isAdmin = (): boolean => {
  const user = getCurrentUser();
  return user?.role === 'admin';
};

// Appointment management functions
export const getAppointments = (): Appointment[] => {
  const appointments = localStorage.getItem('appointments');
  return appointments ? JSON.parse(appointments) : [];
};

export const saveAppointments = (appointments: Appointment[]) => {
  localStorage.setItem('appointments', JSON.stringify(appointments));
};

export const getUserAppointments = (userId: string): Appointment[] => {
  const appointments = getAppointments();
  return appointments.filter(apt => apt.userId === userId);
};

export const createAppointment = (appointmentData: Omit<Appointment, 'id' | 'createdAt'>): Appointment => {
  const appointments = getAppointments();
  const newAppointment: Appointment = {
    ...appointmentData,
    id: `apt-${Date.now()}`,
    createdAt: new Date().toISOString()
  };
  appointments.push(newAppointment);
  saveAppointments(appointments);
  return newAppointment;
};

export const updateAppointment = (id: string, updates: Partial<Appointment>): Appointment | null => {
  const appointments = getAppointments();
  const index = appointments.findIndex(apt => apt.id === id);
  if (index !== -1) {
    appointments[index] = { ...appointments[index], ...updates };
    saveAppointments(appointments);
    return appointments[index];
  }
  return null;
};

export const deleteAppointment = (id: string): boolean => {
  const appointments = getAppointments();
  const filteredAppointments = appointments.filter(apt => apt.id !== id);
  if (filteredAppointments.length !== appointments.length) {
    saveAppointments(filteredAppointments);
    return true;
  }
  return false;
};
