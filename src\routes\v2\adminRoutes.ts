import { Router } from 'express';
import { Request, Response } from 'express';
import { User, Appointment, PaymentConfirmation, Service } from '../../models';
import { authenticate, authorize } from '../../middleware/auth';
import { sendSuccess, sendError } from '../../utils/response';
import { AuthenticatedRequest } from '../../types';

const router = Router();

// All admin routes require authentication and admin role
router.use(authenticate);
router.use(authorize('admin'));

// GET /api/v2/admin/dashboard - Get admin dashboard overview
router.get('/dashboard', async (req: AuthenticatedRequest, res: Response) => {
  try {
    // Get counts for dashboard overview
    const totalUsers = await User.countDocuments({ role: 'user' });
    const totalAppointments = await Appointment.countDocuments();
    const totalServices = await Service.countDocuments({ isActive: true });
    const pendingPayments = await PaymentConfirmation.countDocuments({ status: 'pending' });
    
    // Get recent appointments
    const recentAppointments = await Appointment.find()
      .populate('user', 'firstName lastName email')
      .populate('service', 'name price')
      .sort({ createdAt: -1 })
      .limit(10);
    
    // Get pending payment confirmations
    const pendingPaymentConfirmations = await PaymentConfirmation.find({ status: 'pending' })
      .populate('user', 'firstName lastName email')
      .populate([
        { path: 'appointment', populate: { path: 'service', select: 'name' } },
        { path: 'order' }
      ])
      .sort({ createdAt: -1 })
      .limit(10);
    
    // Get appointment statistics by status
    const appointmentStats = await Appointment.aggregate([
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 }
        }
      }
    ]);
    
    // Get payment confirmation statistics
    const paymentStats = await PaymentConfirmation.aggregate([
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 },
          totalAmount: { $sum: '$amount' }
        }
      }
    ]);
    
    // Format recent appointments
    const formattedAppointments = recentAppointments.map(appointment => ({
      id: appointment._id,
      user: {
        id: (appointment.user as any)._id,
        name: `${(appointment.user as any).firstName} ${(appointment.user as any).lastName}`,
        email: (appointment.user as any).email
      },
      service: {
        id: (appointment.service as any)._id,
        name: (appointment.service as any).name,
        price: (appointment.service as any).price
      },
      date: appointment.date,
      time: appointment.time,
      status: appointment.status,
      totalAmount: (appointment.service as any).price, // Use service price
      createdAt: appointment.createdAt
    }));

    // Format pending payment confirmations
    const formattedPaymentConfirmations = pendingPaymentConfirmations.map(confirmation => ({
      id: confirmation._id,
      user: {
        id: (confirmation.user as any)._id,
        name: `${(confirmation.user as any).firstName} ${(confirmation.user as any).lastName}`,
        email: (confirmation.user as any).email
      },
      appointmentId: confirmation.appointment?._id,
      appointmentService: (confirmation.appointment as any)?.service?.name,
      orderId: confirmation.order?._id,
      amount: confirmation.amount,
      paymentMethod: confirmation.paymentMethod,
      proofImage: confirmation.proofImage,
      createdAt: confirmation.createdAt
    }));
    
    const dashboardData = {
      overview: {
        totalUsers,
        totalAppointments,
        totalServices,
        pendingPayments
      },
      statistics: {
        appointments: appointmentStats.reduce((acc, stat) => {
          acc[stat._id] = stat.count;
          return acc;
        }, {} as any),
        payments: paymentStats.reduce((acc, stat) => {
          acc[stat._id] = {
            count: stat.count,
            totalAmount: stat.totalAmount
          };
          return acc;
        }, {} as any)
      },
      recentAppointments: formattedAppointments,
      pendingPaymentConfirmations: formattedPaymentConfirmations
    };
    
    sendSuccess(res, 'Admin dashboard data retrieved successfully', dashboardData);
  } catch (error) {
    console.error('Get admin dashboard error:', error);
    sendError(res, (error as Error).message);
  }
});

// GET /api/v2/admin/appointments - Get all appointments with pagination and filters
router.get('/appointments', async (req: AuthenticatedRequest, res: Response) => {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 20;
    const status = req.query.status as string;
    const search = req.query.search as string;
    
    const query: any = {};
    if (status) {
      query.status = status;
    }
    
    let appointments;
    let total;
    
    if (search) {
      // Search in user names and service names
      appointments = await Appointment.aggregate([
        {
          $lookup: {
            from: 'users',
            localField: 'user',
            foreignField: '_id',
            as: 'user'
          }
        },
        {
          $lookup: {
            from: 'services',
            localField: 'service',
            foreignField: '_id',
            as: 'service'
          }
        },
        {
          $unwind: '$user'
        },
        {
          $unwind: '$service'
        },
        {
          $match: {
            ...query,
            $or: [
              { 'user.firstName': { $regex: search, $options: 'i' } },
              { 'user.lastName': { $regex: search, $options: 'i' } },
              { 'user.email': { $regex: search, $options: 'i' } },
              { 'service.name': { $regex: search, $options: 'i' } }
            ]
          }
        },
        { $sort: { createdAt: -1 } },
        { $skip: (page - 1) * limit },
        { $limit: limit }
      ]);
      
      total = await Appointment.aggregate([
        {
          $lookup: {
            from: 'users',
            localField: 'user',
            foreignField: '_id',
            as: 'user'
          }
        },
        {
          $lookup: {
            from: 'services',
            localField: 'service',
            foreignField: '_id',
            as: 'service'
          }
        },
        {
          $unwind: '$user'
        },
        {
          $unwind: '$service'
        },
        {
          $match: {
            ...query,
            $or: [
              { 'user.firstName': { $regex: search, $options: 'i' } },
              { 'user.lastName': { $regex: search, $options: 'i' } },
              { 'user.email': { $regex: search, $options: 'i' } },
              { 'service.name': { $regex: search, $options: 'i' } }
            ]
          }
        },
        { $count: 'total' }
      ]);
      
      total = total[0]?.total || 0;
    } else {
      appointments = await Appointment.find(query)
        .populate('user', 'firstName lastName email phone')
        .populate('service', 'name price duration category')
        .sort({ createdAt: -1 })
        .skip((page - 1) * limit)
        .limit(limit);
      
      total = await Appointment.countDocuments(query);
    }
    
    const formattedAppointments = appointments.map((appointment: any) => ({
      id: appointment._id,
      user: {
        id: appointment.user._id,
        name: `${appointment.user.firstName} ${appointment.user.lastName}`,
        email: appointment.user.email,
        phone: appointment.user.phone
      },
      service: {
        id: appointment.service._id,
        name: appointment.service.name,
        price: appointment.service.price,
        duration: appointment.service.duration,
        category: appointment.service.category
      },
      date: appointment.date,
      time: appointment.time,
      status: appointment.status,
      customerInfo: appointment.customerInfo,
      totalAmount: appointment.service.price, // Use service price
      notes: appointment.message,
      createdAt: appointment.createdAt
    }));
    
    sendSuccess(res, 'Appointments retrieved successfully', {
      appointments: formattedAppointments,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Get admin appointments error:', error);
    sendError(res, (error as Error).message);
  }
});

// PUT /api/v2/admin/appointments/:id/status - Update appointment status
router.put('/appointments/:id/status', async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { id } = req.params;
    const { status } = req.body;
    
    if (!['pending', 'confirmed', 'completed', 'cancelled'].includes(status)) {
      sendError(res, 'Invalid status', undefined, 400);
      return;
    }
    
    const appointment = await Appointment.findById(id)
      .populate('user', 'firstName lastName email')
      .populate('service', 'name price');
    
    if (!appointment) {
      sendError(res, 'Appointment not found', undefined, 404);
      return;
    }
    
    appointment.status = status;
    await appointment.save();
    
    const responseData = {
      id: appointment._id,
      status: appointment.status,
      user: {
        name: `${(appointment.user as any).firstName} ${(appointment.user as any).lastName}`,
        email: (appointment.user as any).email
      },
      service: {
        name: (appointment.service as any).name
      },
      date: appointment.date,
      time: appointment.time,
      updatedAt: appointment.updatedAt
    };
    
    sendSuccess(res, 'Appointment status updated successfully', responseData);
  } catch (error) {
    console.error('Update appointment status error:', error);
    sendError(res, (error as Error).message);
  }
});

// GET /api/v2/admin/payment-confirmations - Get all payment confirmations with pagination and filters
router.get('/payment-confirmations', async (req: AuthenticatedRequest, res: Response) => {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 20;
    const status = req.query.status as string;

    const query: any = {};
    if (status) {
      query.status = status;
    }

    const paymentConfirmations = await PaymentConfirmation.find(query)
      .populate('user', 'firstName lastName email phone')
      .populate([
        { path: 'appointment', populate: { path: 'service', select: 'name price' } },
        { path: 'order' }
      ])
      .sort({ createdAt: -1 })
      .skip((page - 1) * limit)
      .limit(limit);

    const total = await PaymentConfirmation.countDocuments(query);

    const formattedConfirmations = paymentConfirmations.map(confirmation => ({
      id: confirmation._id,
      user: {
        id: (confirmation.user as any)._id,
        name: `${(confirmation.user as any).firstName} ${(confirmation.user as any).lastName}`,
        email: (confirmation.user as any).email,
        phone: (confirmation.user as any).phone
      },
      appointmentId: confirmation.appointment?._id,
      appointmentService: (confirmation.appointment as any)?.service?.name,
      appointmentServicePrice: (confirmation.appointment as any)?.service?.price,
      orderId: confirmation.order?._id,
      amount: confirmation.amount,
      paymentMethod: confirmation.paymentMethod,
      proofImage: confirmation.proofImage,
      notes: confirmation.notes,
      status: confirmation.status,
      verifiedAt: confirmation.verifiedAt,
      rejectionReason: confirmation.rejectionReason,
      createdAt: confirmation.createdAt
    }));

    sendSuccess(res, 'Payment confirmations retrieved successfully', {
      paymentConfirmations: formattedConfirmations,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Get admin payment confirmations error:', error);
    sendError(res, (error as Error).message);
  }
});

// PUT /api/v2/admin/payment-confirmations/:id/status - Update payment confirmation status
router.put('/payment-confirmations/:id/status', async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { id } = req.params;
    const { status, rejectionReason } = req.body;

    if (!['verified', 'rejected'].includes(status)) {
      sendError(res, 'Invalid status. Must be "verified" or "rejected"', undefined, 400);
      return;
    }

    if (status === 'rejected' && !rejectionReason) {
      sendError(res, 'Rejection reason is required when rejecting payment confirmation', undefined, 400);
      return;
    }

    const paymentConfirmation = await PaymentConfirmation.findById(id)
      .populate('user', 'firstName lastName email')
      .populate([
        { path: 'appointment', populate: { path: 'service', select: 'name' } },
        { path: 'order' }
      ]);

    if (!paymentConfirmation) {
      sendError(res, 'Payment confirmation not found', undefined, 404);
      return;
    }

    paymentConfirmation.status = status;
    paymentConfirmation.verifiedBy = req.user!._id as any;
    paymentConfirmation.verifiedAt = new Date();

    if (status === 'rejected') {
      paymentConfirmation.rejectionReason = rejectionReason;
    }

    await paymentConfirmation.save();

    const responseData = {
      id: paymentConfirmation._id,
      status: paymentConfirmation.status,
      user: {
        name: `${(paymentConfirmation.user as any).firstName} ${(paymentConfirmation.user as any).lastName}`,
        email: (paymentConfirmation.user as any).email
      },
      amount: paymentConfirmation.amount,
      paymentMethod: paymentConfirmation.paymentMethod,
      verifiedAt: paymentConfirmation.verifiedAt,
      rejectionReason: paymentConfirmation.rejectionReason,
      updatedAt: paymentConfirmation.updatedAt
    };

    sendSuccess(res, 'Payment confirmation status updated successfully', responseData);
  } catch (error) {
    console.error('Update payment confirmation status error:', error);
    sendError(res, (error as Error).message);
  }
});

export default router;
