import { useState, useEffect } from 'react';
import { appointmentAPI, type Appointment } from '../../utils/appointmentAPI';
import { type User, dashboardAPI, clearAuthData } from '../../utils/api';

interface UserDashboardProps {
  currentUser: User | null;
  onLogout: () => void;
  onBookNew: () => void;
}

export default function UserDashboard({ currentUser, onLogout, onBookNew }: UserDashboardProps) {
  const [appointments, setAppointments] = useState<Appointment[]>([]);
  const [activeTab, setActiveTab] = useState<'upcoming' | 'past' | 'profile'>('upcoming');
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (currentUser) {
      fetchUserAppointments();
    }
  }, [currentUser]);

  const fetchUserAppointments = async () => {
    try {
      setLoading(true);
      // Try to use the new dashboard API first, fallback to appointment API
      try {
        const response = await dashboardAPI.getUserAppointments();
        console.log('Dashboard API response:', response);

        // Handle different response formats
        let appointmentsData = [];
        if (response && response.success && Array.isArray(response.data)) {
          appointmentsData = response.data;
        } else if (Array.isArray(response)) {
          appointmentsData = response;
        } else if (response && Array.isArray(response.appointments)) {
          appointmentsData = response.appointments;
        } else {
          console.warn('Unexpected response format from dashboard API:', response);
          appointmentsData = [];
        }

        setAppointments(appointmentsData);
      } catch (dashboardError) {
        console.warn('Dashboard API failed, trying appointment API:', dashboardError);

        try {
          const response = await appointmentAPI.getUserAppointments();
          console.log('Appointment API response:', response);

          // Handle different response formats
          let appointmentsData = [];
          if (response && response.success && Array.isArray(response.data)) {
            appointmentsData = response.data;
          } else if (Array.isArray(response)) {
            appointmentsData = response;
          } else if (response && Array.isArray(response.appointments)) {
            appointmentsData = response.appointments;
          } else {
            console.warn('Unexpected response format from appointment API:', response);
            appointmentsData = [];
          }

          setAppointments(appointmentsData);
        } catch (appointmentError) {
          console.error('Both APIs failed:', { dashboardError, appointmentError });
          setAppointments([]);
        }
      }
    } catch (error) {
      console.error('Error fetching appointments:', error);
      setAppointments([]);
    } finally {
      setLoading(false);
    }
  };

  const handleCancelAppointment = async (appointmentId: string) => {
    if (window.confirm('Are you sure you want to cancel this appointment?')) {
      try {
        await appointmentAPI.cancelAppointment(appointmentId);
        await fetchUserAppointments(); // Refresh the list
        alert('Appointment cancelled successfully');
      } catch (error) {
        console.error('Error cancelling appointment:', error);
        alert('Failed to cancel appointment. Please try again.');
      }
    }
  };

  const handleLogout = () => {
    clearAuthData();
    onLogout();
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const formatTime = (timeString: string) => {
    return timeString;
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'confirmed': return '#28a745';
      case 'pending': return '#ffc107';
      case 'completed': return '#6c757d';
      case 'cancelled': return '#dc3545';
      default: return '#6c757d';
    }
  };

  // Ensure appointments is always an array
  const appointmentsArray = Array.isArray(appointments) ? appointments : [];

  const upcomingAppointments = appointmentsArray.filter(apt =>
    new Date(apt.date) >= new Date() && apt.status !== 'cancelled'
  );

  const pastAppointments = appointmentsArray.filter(apt =>
    new Date(apt.date) < new Date() || apt.status === 'completed' || apt.status === 'cancelled'
  );

  if (!currentUser) {
    return <div>Please log in to view your dashboard.</div>;
  }

  if (loading) {
    return (
      <div className="dashboard-container">
        <div className="loading-container">
          <p>Loading your dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="dashboard-container">
      {/* Header */}
      <header className="dashboard-header">
        <div className="dashboard-header-content">
          <div className="dashboard-logo">
            <h1>dammyspicybeauty</h1>
          </div>
          <div className="dashboard-user-menu">
            <span className="welcome-text">Welcome, {currentUser.firstName || currentUser.name?.split(' ')[0] || 'User'}!</span>
            <button className="logout-button" onClick={handleLogout}>
              Logout
            </button>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="dashboard-main">
        <div className="dashboard-content">
          {/* Quick Actions */}
          <div className="quick-actions">
            <button className="action-button primary" onClick={onBookNew}>
              📅 Book New Appointment
            </button>
            <div className="stats-grid">
              <div className="stat-card">
                <h3>{upcomingAppointments.length}</h3>
                <p>Upcoming Appointments</p>
              </div>
              <div className="stat-card">
                <h3>{pastAppointments.length}</h3>
                <p>Past Appointments</p>
              </div>
              <div className="stat-card">
                <h3>${appointmentsArray.reduce((sum, apt) => sum + (apt.totalPrice || 0), 0).toFixed(2)}</h3>
                <p>Total Spent</p>
              </div>
            </div>
          </div>

          {/* Navigation Tabs */}
          <div className="dashboard-tabs">
            <button 
              className={`tab-button ${activeTab === 'upcoming' ? 'active' : ''}`}
              onClick={() => setActiveTab('upcoming')}
            >
              Upcoming Appointments ({upcomingAppointments.length})
            </button>
            <button 
              className={`tab-button ${activeTab === 'past' ? 'active' : ''}`}
              onClick={() => setActiveTab('past')}
            >
              Past Appointments ({pastAppointments.length})
            </button>
            <button 
              className={`tab-button ${activeTab === 'profile' ? 'active' : ''}`}
              onClick={() => setActiveTab('profile')}
            >
              Profile
            </button>
          </div>

          {/* Tab Content */}
          <div className="tab-content">
            {activeTab === 'upcoming' && (
              <div className="appointments-list">
                {upcomingAppointments.length === 0 ? (
                  <div className="empty-state">
                    <h3>No upcoming appointments</h3>
                    <p>Book your next appointment to get started!</p>
                    <button className="action-button primary" onClick={onBookNew}>
                      Book Appointment
                    </button>
                  </div>
                ) : (
                  upcomingAppointments.map(appointment => (
                    <div key={appointment.id} className="appointment-card">
                      <div className="appointment-header">
                        <h4>{appointment.serviceName}</h4>
                        <span 
                          className="status-badge"
                          style={{ backgroundColor: getStatusColor(appointment.status) }}
                        >
                          {appointment.status.toUpperCase()}
                        </span>
                      </div>
                      <div className="appointment-details">
                        <p><strong>Date:</strong> {formatDate(appointment.date)}</p>
                        <p><strong>Time:</strong> {formatTime(appointment.time)}</p>
                        <p><strong>Total:</strong> ${appointment.totalPrice.toFixed(2)}</p>
                        {appointment.addOns.length > 0 && (
                          <div className="add-ons">
                            <strong>Add-ons:</strong>
                            <ul>
                              {appointment.addOns.map(addon => (
                                <li key={addon.id}>{addon.name} (+${addon.price})</li>
                              ))}
                            </ul>
                          </div>
                        )}
                        {appointment.notes && (
                          <div className="appointment-notes">
                            <strong>Notes:</strong> {appointment.notes}
                          </div>
                        )}
                      </div>
                      <div className="appointment-actions">
                        {appointment.status === 'pending' && (
                          <button
                            className="action-button danger"
                            onClick={() => handleCancelAppointment(appointment.id)}
                          >
                            Cancel Appointment
                          </button>
                        )}
                      </div>
                    </div>
                  ))
                )}
              </div>
            )}

            {activeTab === 'past' && (
              <div className="appointments-list">
                {pastAppointments.length === 0 ? (
                  <div className="empty-state">
                    <h3>No past appointments</h3>
                    <p>Your appointment history will appear here.</p>
                  </div>
                ) : (
                  pastAppointments.map(appointment => (
                    <div key={appointment.id} className="appointment-card past">
                      <div className="appointment-header">
                        <h4>{appointment.serviceName}</h4>
                        <span 
                          className="status-badge"
                          style={{ backgroundColor: getStatusColor(appointment.status) }}
                        >
                          {appointment.status.toUpperCase()}
                        </span>
                      </div>
                      <div className="appointment-details">
                        <p><strong>Date:</strong> {formatDate(appointment.date)}</p>
                        <p><strong>Time:</strong> {formatTime(appointment.time)}</p>
                        <p><strong>Total:</strong> ${appointment.totalPrice.toFixed(2)}</p>
                        {appointment.addOns.length > 0 && (
                          <div className="add-ons">
                            <strong>Add-ons:</strong>
                            <ul>
                              {appointment.addOns.map(addon => (
                                <li key={addon.id}>{addon.name} (+${addon.price})</li>
                              ))}
                            </ul>
                          </div>
                        )}
                      </div>
                    </div>
                  ))
                )}
              </div>
            )}

            {activeTab === 'profile' && (
              <div className="profile-section">
                <div className="profile-card">
                  <h3>Personal Information</h3>
                  <div className="profile-info">
                    <div className="info-row">
                      <label>Name:</label>
                      <span>{currentUser.name || `${currentUser.firstName || ''} ${currentUser.lastName || ''}`.trim()}</span>
                    </div>
                    <div className="info-row">
                      <label>Email:</label>
                      <span>{currentUser.email}</span>
                    </div>
                    <div className="info-row">
                      <label>Phone:</label>
                      <span>{currentUser.phone || 'Not provided'}</span>
                    </div>
                    <div className="info-row">
                      <label>Role:</label>
                      <span>{currentUser.role}</span>
                    </div>
                  </div>
                  <button className="action-button secondary">
                    Edit Profile
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </main>
    </div>
  );
}
