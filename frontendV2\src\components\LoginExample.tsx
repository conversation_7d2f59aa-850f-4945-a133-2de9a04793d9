import React, { useState } from 'react';
import { saveLoginResponseData, dashboardAPI, getCurrentUser, isAuthenticated } from '../utils/api';

// Example component showing how to handle the login response you provided
export default function LoginExample() {
  const [dashboardData, setDashboardData] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Your login response data
  const exampleLoginResponse = {
    "success": true,
    "message": "Login successful",
    "data": {
      "user": {
        "notificationPreferences": {
          "email": true,
          "sms": false,
          "push": true
        },
        "_id": "685930e41037604a323737e7",
        "name": "<PERSON> Olawore",
        "firstName": "Joshua",
        "lastName": "Olawore",
        "email": "<EMAIL>",
        "phone": "+2347049670618",
        "role": "user",
        "isVerified": false,
        "favorites": [],
        "createdAt": "2025-06-23T10:48:04.934Z",
        "updatedAt": "2025-06-27T17:55:03.344Z",
        "__v": 0
      },
      "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************.CJqCswkXSgi51bDHOFdU_mw-KEXCaeT55bb0AgWsjUI",
      "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************.Sctjw_7vl5Fr8rm7R8YDRi1C7eh9UgIXNVRGCsUwk3A"
    }
  };

  // Function to simulate saving login data and calling dashboard
  const handleSaveLoginAndCallDashboard = async () => {
    try {
      setLoading(true);
      setError(null);

      // Step 1: Save the login response data to localStorage
      console.log('Saving login response data...');
      saveLoginResponseData(exampleLoginResponse);

      // Step 2: Verify data was saved
      const currentUser = getCurrentUser();
      const isAuth = isAuthenticated();
      console.log('Current user:', currentUser);
      console.log('Is authenticated:', isAuth);

      // Step 3: Call the dashboard endpoint
      console.log('Calling dashboard API...');
      const dashboard = await dashboardAPI.getUserDashboard();
      setDashboardData(dashboard);
      console.log('Dashboard data:', dashboard);

    } catch (err: any) {
      console.error('Error:', err);
      setError(err.message || 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  // Function to call dashboard with stored data
  const handleCallDashboard = async () => {
    try {
      setLoading(true);
      setError(null);

      const dashboard = await dashboardAPI.getUserDashboard();
      setDashboardData(dashboard);
      console.log('Dashboard data:', dashboard);

    } catch (err: any) {
      console.error('Error calling dashboard:', err);
      setError(err.message || 'Failed to fetch dashboard data');
    } finally {
      setLoading(false);
    }
  };

  // Function to call user appointments
  const handleCallUserAppointments = async () => {
    try {
      setLoading(true);
      setError(null);

      const appointments = await dashboardAPI.getUserAppointments();
      setDashboardData(appointments);
      console.log('User appointments:', appointments);

    } catch (err: any) {
      console.error('Error calling user appointments:', err);
      setError(err.message || 'Failed to fetch user appointments');
    } finally {
      setLoading(false);
    }
  };

  const currentUser = getCurrentUser();
  const isAuth = isAuthenticated();

  return (
    <div style={{ padding: '20px', maxWidth: '800px', margin: '0 auto' }}>
      <h2>Login Response Handler Example</h2>
      
      {/* Current Auth Status */}
      <div style={{ marginBottom: '20px', padding: '15px', backgroundColor: '#f5f5f5', borderRadius: '5px' }}>
        <h3>Current Authentication Status</h3>
        <p><strong>Is Authenticated:</strong> {isAuth ? 'Yes' : 'No'}</p>
        {currentUser && (
          <div>
            <p><strong>User ID:</strong> {currentUser._id || currentUser.id}</p>
            <p><strong>Name:</strong> {currentUser.name || `${currentUser.firstName} ${currentUser.lastName}`}</p>
            <p><strong>Email:</strong> {currentUser.email}</p>
            <p><strong>Role:</strong> {currentUser.role}</p>
          </div>
        )}
      </div>

      {/* Action Buttons */}
      <div style={{ marginBottom: '20px' }}>
        <button 
          onClick={handleSaveLoginAndCallDashboard}
          disabled={loading}
          style={{ 
            marginRight: '10px', 
            padding: '10px 15px', 
            backgroundColor: '#007bff', 
            color: 'white', 
            border: 'none', 
            borderRadius: '5px',
            cursor: loading ? 'not-allowed' : 'pointer'
          }}
        >
          {loading ? 'Processing...' : 'Save Login Data & Call Dashboard'}
        </button>

        <button 
          onClick={handleCallDashboard}
          disabled={loading || !isAuth}
          style={{ 
            marginRight: '10px', 
            padding: '10px 15px', 
            backgroundColor: '#28a745', 
            color: 'white', 
            border: 'none', 
            borderRadius: '5px',
            cursor: (loading || !isAuth) ? 'not-allowed' : 'pointer'
          }}
        >
          Call Dashboard API
        </button>

        <button 
          onClick={handleCallUserAppointments}
          disabled={loading || !isAuth}
          style={{ 
            padding: '10px 15px', 
            backgroundColor: '#17a2b8', 
            color: 'white', 
            border: 'none', 
            borderRadius: '5px',
            cursor: (loading || !isAuth) ? 'not-allowed' : 'pointer'
          }}
        >
          Call User Appointments API
        </button>
      </div>

      {/* Error Display */}
      {error && (
        <div style={{ 
          marginBottom: '20px', 
          padding: '15px', 
          backgroundColor: '#f8d7da', 
          color: '#721c24', 
          borderRadius: '5px',
          border: '1px solid #f5c6cb'
        }}>
          <strong>Error:</strong> {error}
        </div>
      )}

      {/* Dashboard Data Display */}
      {dashboardData && (
        <div style={{ 
          marginTop: '20px', 
          padding: '15px', 
          backgroundColor: '#d4edda', 
          borderRadius: '5px',
          border: '1px solid #c3e6cb'
        }}>
          <h3>Dashboard Response:</h3>
          <pre style={{ 
            backgroundColor: '#f8f9fa', 
            padding: '10px', 
            borderRadius: '3px', 
            overflow: 'auto',
            fontSize: '12px'
          }}>
            {JSON.stringify(dashboardData, null, 2)}
          </pre>
        </div>
      )}

      {/* Example Login Response Display */}
      <div style={{ 
        marginTop: '30px', 
        padding: '15px', 
        backgroundColor: '#e2e3e5', 
        borderRadius: '5px'
      }}>
        <h3>Example Login Response (Your Data):</h3>
        <pre style={{ 
          backgroundColor: '#f8f9fa', 
          padding: '10px', 
          borderRadius: '3px', 
          overflow: 'auto',
          fontSize: '11px'
        }}>
          {JSON.stringify(exampleLoginResponse, null, 2)}
        </pre>
      </div>
    </div>
  );
}
